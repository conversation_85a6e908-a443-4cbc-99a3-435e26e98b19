import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrdersService } from './orders.service';
import { Order } from './entities/order.entity';
import { OrderItem } from './entities/order-item.entity';
import { Product } from '../products/entities/product.entity';
import { NotFoundException, BadRequestException } from '@nestjs/common';

describe('OrdersService', () => {
  let service: OrdersService;
  let orderRepository: jest.Mocked<Repository<Order>>;
  let orderItemRepository: jest.Mocked<Repository<OrderItem>>;
  let productRepository: jest.Mocked<Repository<Product>>;

  const mockRepository = () => ({
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    })),
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrdersService,
        {
          provide: getRepositoryToken(Order),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(OrderItem),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(Product),
          useValue: mockRepository(),
        },
      ],
    }).compile();

    service = module.get<OrdersService>(OrdersService);
    orderRepository = module.get(getRepositoryToken(Order));
    orderItemRepository = module.get(getRepositoryToken(OrderItem));
    productRepository = module.get(getRepositoryToken(Product));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findOne', () => {
    it('should return an order when found', async () => {
      const mockOrder = {
        id: '1',
        userId: 'user1',
        total: 100,
        status: 'pending',
      };

      orderRepository.findOne.mockResolvedValue(mockOrder as Order);

      const result = await service.findOne('1');
      expect(result).toEqual(mockOrder);
      expect(orderRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['user', 'items', 'items.product'],
      });
    });

    it('should throw NotFoundException when order not found', async () => {
      orderRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('create', () => {
    const mockUser = { id: 'user1', email: '<EMAIL>', role: 'retailer' };
    const mockProduct = {
      id: 'product1',
      title: 'Test Product',
      price: 50,
      stock: 10,
    };

    const createOrderDto = {
      items: [{ productId: 'product1', quantity: 2 }],
      shippingAddress: '123 Test St',
    };

    it('should create an order successfully', async () => {
      productRepository.findOne.mockResolvedValue(mockProduct as Product);
      orderRepository.create.mockReturnValue({ id: 'order1' } as Order);
      orderRepository.save.mockResolvedValue({ id: 'order1' } as Order);
      orderItemRepository.create.mockReturnValue({} as OrderItem);
      orderItemRepository.save.mockResolvedValue({} as OrderItem);
      productRepository.update.mockResolvedValue(undefined);
      
      // Mock the findOne call that happens at the end
      orderRepository.findOne.mockResolvedValue({
        id: 'order1',
        userId: 'user1',
        total: 111, // 50*2 + 10 (tax) + 10 (shipping) + 1 (rounding)
      } as Order);

      const result = await service.create(createOrderDto, mockUser as any);

      expect(result).toBeDefined();
      expect(productRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'product1' }
      });
      expect(orderRepository.create).toHaveBeenCalled();
      expect(orderRepository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException when product not found', async () => {
      productRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createOrderDto, mockUser as any))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when insufficient stock', async () => {
      const lowStockProduct = { ...mockProduct, stock: 1 };
      productRepository.findOne.mockResolvedValue(lowStockProduct as Product);

      await expect(service.create(createOrderDto, mockUser as any))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    it('should return all orders', async () => {
      const mockOrders = [{ id: '1' }, { id: '2' }];
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockOrders),
      };
      
      orderRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      const result = await service.findAll();
      expect(result).toEqual(mockOrders);
    });

    it('should filter by userId when provided', async () => {
      const mockOrders = [{ id: '1', userId: 'user1' }];
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockOrders),
      };
      
      orderRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      const result = await service.findAll('user1');
      expect(result).toEqual(mockOrders);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('order.userId = :userId', { userId: 'user1' });
    });
  });
});
