'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Loader2, ArrowLeft, ShoppingCart, Star, Heart, Share2, MessageCircle, Shield, Truck, RotateCcw } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { fetchProductById, Product } from '@/services/productService';
import { useAuthStore } from '@/store/authStore';

export default function ProductDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const { user } = useAuthStore();

  useEffect(() => {
    const loadProduct = async () => {
      try {
        setLoading(true);
        setError(null);
        const productData = await fetchProductById(id);
        setProduct(productData);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError('Failed to load product. Please try again later.');
        toast.error('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadProduct();
    }
  }, [id]);

  const handleQuantityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    setQuantity(Math.max(1, Math.min(value, product?.stock || 1)));
  };

  const handleAddToCart = () => {
    if (!product) return;
    if (!user) {
      toast.error('Please log in to add items to your cart');
      return;
    }
    toast.success(`${quantity} x ${product.title} added to your cart!`);
  };

  const handleWishlist = () => {
    if (!user) {
      toast.error('Please log in to add items to your wishlist');
      return;
    }
    setIsWishlisted(!isWishlisted);
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.title,
        text: product?.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Product link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => window.location.reload()}>Retry</Button>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <p className="text-lg mb-4">Product not found</p>
        <Link href="/marketplace">
          <Button>Back to Marketplace</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
        <Link href="/marketplace" className="hover:text-gray-900">Marketplace</Link>
        <span>/</span>
        <Link href={`/marketplace?category=${product?.category?.id}`} className="hover:text-gray-900">
          {product?.category?.name}
        </Link>
        <span>/</span>
        <span className="text-gray-900">{product?.title}</span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
            {product?.images && product.images.length > 0 ? (
              <Image
                src={product.images[selectedImageIndex]?.url || '/api/placeholder/600/600'}
                alt={product.title}
                fill
                className="object-cover"
                priority
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gray-200 rounded-lg"></div>
                  <p>No Image Available</p>
                </div>
              </div>
            )}
          </div>

          {/* Thumbnail Images */}
          {product?.images && product.images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={image.id}
                  type="button"
                  onClick={() => setSelectedImageIndex(index)}
                  title={`View image ${index + 1}`}
                  className={`aspect-square bg-gray-100 rounded-lg overflow-hidden relative border-2 transition-colors ${
                    selectedImageIndex === index ? 'border-blue-500' : 'border-transparent hover:border-gray-300'
                  }`}
                >
                  <Image
                    src={image.url}
                    alt={`${product.title} - Image ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        <div>
          <h1 className="text-3xl font-bold mb-2">{product.title}</h1>
          <p className="text-xl font-semibold text-green-600 mb-4">${product.price.toFixed(2)}</p>
          
          <div className="mb-6">
            <p className="text-gray-700 mb-4">{product.description}</p>
            <p className="text-sm text-gray-500 mb-2">
              Category: {product.category ? product.category.name : 'Uncategorized'}
            </p>
            <p className="text-sm text-gray-500 mb-2">
              Seller: {product.seller ? product.seller.name : 'Unknown'}
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Listed on: {new Date(product.createdAt).toLocaleDateString()}
            </p>
            <p className={`font-semibold ${product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {product.stock > 0 ? `In Stock (${product.stock} available)` : 'Out of Stock'}
            </p>
          </div>

          {product.stock > 0 && (
            <Card className="mb-6">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <label htmlFor="quantity" className="font-medium">
                    Quantity:
                  </label>
                  <input
                    type="number"
                    id="quantity"
                    min="1"
                    max={product.stock}
                    value={quantity}
                    onChange={handleQuantityChange}
                    className="w-20 border rounded p-2 text-center"
                  />
                </div>
                <Button
                  onClick={handleAddToCart}
                  className="w-full"
                  disabled={!user}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add to Cart
                </Button>
                {!user && (
                  <p className="text-sm text-red-500 mt-2 text-center">
                    Please <Link href="/login" className="underline">log in</Link> to add items to your cart
                  </p>
                )}
              </CardContent>
            </Card>
          )}

          {user && product.seller && user.id !== product.seller.id && (
            <Button variant="outline" className="w-full">
              Contact Seller
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}