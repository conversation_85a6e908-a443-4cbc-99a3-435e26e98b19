import { api } from '@/utils/api';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

export interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  stock: number;
  isActive: boolean;
  seller: {
    id: string;
    username: string;
    fullName?: string;
    businessName?: string;
  };
  category: {
    id: string;
    name: string;
  };
  images: {
    id: string;
    url: string;
  }[];
  createdAt: string;
  updatedAt: string;
  rating?: number;
  ratingCount: number;
  viewCount: number;
  purchaseCount: number;
}

export interface ProductsResponse {
  products: Product[];
  totalPages: number;
}

export interface FilterProductsParams {
  search?: string;
  sort?: 'popular' | 'newest' | 'price-low' | 'price-high';
  page?: number;
  limit?: number;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  userId?: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
}

export const fetchProducts = async (params?: FilterProductsParams): Promise<ProductsResponse> => {
  const queryParams = new URLSearchParams();

  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
  }

  const response = await api.get(`/api/products?${queryParams.toString()}`);
  return response.data;
};

export const fetchProductById = async (id: string): Promise<Product> => {
  const response = await api.get(`/api/products/${id}`);
  return response.data;
};

export const fetchCategories = async (): Promise<Category[]> => {
  const response = await api.get('/api/categories');
  return response.data;
};

export const createProduct = async (product: {
  title: string;
  description: string;
  price: number;
  stock: number;
  categoryId: string;
  images: string[];
}): Promise<Product> => {
  const response = await api.post('/api/products', product);
  return response.data;
};